import { NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET() {
  try {
    // Try to read the config file from the public directory
    const configPath = join(process.cwd(), 'public', 'admin', 'config.yml');
    const configContent = readFileSync(configPath, 'utf8');
    
    return new NextResponse(configContent, {
      headers: {
        'Content-Type': 'text/yaml',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('Error reading config file:', error);
    return NextResponse.json({ 
      error: 'Config file not found',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 404 });
  }
}
