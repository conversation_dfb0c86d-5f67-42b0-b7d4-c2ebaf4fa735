{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install --legacy-peer-deps", "devCommand": "npm run dev", "functions": {"src/app/api/auth/route.ts": {"maxDuration": 30}}, "rewrites": [{"source": "/admin", "destination": "/admin/index.html"}, {"source": "/admin/config.yml", "destination": "/api/admin/config"}], "headers": [{"source": "/admin/config.yml", "headers": [{"key": "Content-Type", "value": "text/yaml"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type"}]}, {"source": "/admin/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}]}]}