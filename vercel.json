{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install --legacy-peer-deps", "devCommand": "npm run dev", "functions": {"src/app/api/auth/route.ts": {"maxDuration": 30}}, "rewrites": [{"source": "/admin", "destination": "/admin/index.html"}], "headers": [{"source": "/admin/config.yml", "headers": [{"key": "Content-Type", "value": "text/yaml"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/admin/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}]}]}